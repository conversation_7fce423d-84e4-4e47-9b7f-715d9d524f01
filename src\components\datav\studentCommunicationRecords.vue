<template>
  <div id="student-communication-records">
    <!-- 组件标题 -->
    <div class="component-header">
      <h3>学生进出通信记录</h3>
    </div>

    <!-- Tab 切换按钮 -->
    <div class="tab-container">
      <div class="tab-item" :class="{ active: activeTab === 'entry' }" @click="switchTab('entry')">
        <div class="tab-icon">📥</div>
        <div class="tab-text">进入记录</div>
        <div class="tab-count">({{ entryData.data.length }})</div>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'exit' }" @click="switchTab('exit')">
        <div class="tab-icon">📤</div>
        <div class="tab-text">离开记录</div>
        <div class="tab-count">({{ exitData.data.length }})</div>
      </div>
    </div>

    <!-- 滚动表格 -->
    <div class="board-content">
      <dv-scroll-board :config="config" />
    </div>
    <!-- 图片轮播 -->
    <div class="image-carousel">
      <div class="carousel-container" ref="carouselContainer">
        <div class="carousel-track" :style="{ transform: `translateX(${translateX}px)` }">
          <div v-for="(image, index) in carouselImages" :key="index" class="carousel-item">
            <img :src="image" :alt="`轮播图片${index + 1}`" />
          </div>
          <!-- 复制一份用于无缝循环 -->
          <div v-for="(image, index) in carouselImages" :key="`copy-${index}`" class="carousel-item">
            <img :src="image" :alt="`轮播图片${index + 1}`" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentCommunicationRecords',
  data() {
    return {
            // 轮播相关数据
      carouselImages: [
        require('./img/图片1.jpg'),
        require('./img/图片2.png'),
        require('./img/图片3.jpg')
      ],
      translateX: 0,
      carouselTimer: null,
      itemWidth: 60, // 每个轮播项的宽度
      speed: 1 // 滚动速度
      activeTab: 'entry', // 当前激活的tab，默认为进入记录
      autoSwitchTimer: null, // 自动切换定时器
      // 进入记录数据
      entryData: {
        header: ['学生姓名', '二级学院', '进入时间'],
        data: [
          ['张明', '传媒学院', '08:30:15'],
          ['李华', '传媒学院', '08:45:22'],
          ['王芳', '传媒学院', '09:12:08'],
          ['刘强', '传媒学院', '09:25:33'],
          ['陈静', '传媒学院', '09:38:45'],
          ['赵伟', '传媒学院', '10:15:12'],
          ['孙丽', '传媒学院', '10:28:56'],
          ['周杰', '传媒学院', '10:45:33'],
          ['吴敏', '纺织服装学院', '11:02:18'],
          ['郑浩', '新媒体产业学院', '11:18:44']
        ]
      },
      // 离开记录数据
      exitData: {
        header: ['学生姓名', '二级学院', '离开时间'],
        data: [
          ['马超', '传媒学院', '17:30:25'],
          ['林雪', '传媒学院', '17:45:18'],
          ['黄磊', '传媒学院', '18:12:33'],
          ['许晴', '传媒学院', '18:25:47'],
          ['邓涛', '传媒学院', '18:38:52'],
          ['谢娜', '交通工程学院', '19:05:16'],
          ['袁华', '传媒学院', '19:18:29'],
          ['冯小刚', '传媒学院', '19:32:41'],
          ['葛优', '纺织服装学院', '19:45:55'],
          ['范冰冰', '传媒学院', '20:02:08']
        ]
      }
    }
  },
  computed: {
    config() {
      const baseConfig = {
        index: true,
        // columnWidth: [70, 140, 100],
        align: ['center', 'center', 'left', 'center', 'center', 'center'],
        rowNum: 4,
        headerBGC: 'rgb(33 50 95)', // 表头背景色
        headerHeight: 40,
        oddRowBGC: 'rgba(0, 44, 81, 0.8)',
        evenRowBGC: 'rgba(10, 29, 50, 0.8)',
        waitTime: 3000,
        carousel: 'single',
        hoverPause: true
      }

      if (this.activeTab === 'entry') {
        return {
          ...baseConfig,
          header: this.entryData.header,
          data: this.entryData.data
        }
      } else {
        return {
          ...baseConfig,
          header: this.exitData.header,
          data: this.exitData.data
        }
      }
    },
    // 今日进入人数
    todayEntryCount() {
      return this.entryData.data.length
    },
    // 今日离开人数
    todayExitCount() {
      return this.exitData.data.length
    },
    // 当前在校人数（简单计算：进入 - 离开）
    currentInSchool() {
      return Math.max(0, this.todayEntryCount - this.todayExitCount)
    }
  },
  methods: {
    
    switchTab(tab) {
      this.activeTab = tab
      // 手动切换时重置自动切换定时器
      this.resetAutoSwitch()
    },
    // 自动切换tab
    autoSwitchTab() {
      this.activeTab = this.activeTab === 'entry' ? 'exit' : 'entry'
    },
    // 启动自动切换
    startAutoSwitch() {
      this.autoSwitchTimer = setInterval(() => {
        this.autoSwitchTab()
      }, 60000) // 60秒切换一次
    },
    // 重置自动切换定时器
    resetAutoSwitch() {
      if (this.autoSwitchTimer) {
        clearInterval(this.autoSwitchTimer)
      }
      this.startAutoSwitch()
    },
    // 模拟实时数据更新
    updateData() {
      // 这里可以添加实时数据更新逻辑
      // 例如：从API获取最新的进出记录
    }
  },
  mounted() {
    // 组件挂载后可以开始数据更新
    this.updateData()
    // 启动自动切换
    this.startAutoSwitch()
    // 可以设置定时器定期更新数据
    // this.dataUpdateTimer = setInterval(this.updateData, 30000) // 每30秒更新一次
  },
  beforeDestroy() {
    // 清理定时器
    if (this.dataUpdateTimer) {
      clearInterval(this.dataUpdateTimer)
    }
    // 清理自动切换定时器
    if (this.autoSwitchTimer) {
      clearInterval(this.autoSwitchTimer)
    }
  }
}
</script>

<style lang="less">
#student-communication-records {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(1, 153, 209, 0.3); // 边框
  border-radius: 15px;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // 组件标题
  .component-header {
    text-align: center;

    h3 {
      margin: 0 0 5px 0;
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }

    .header-subtitle {
      color: #b8c5d1;
      font-size: 12px;
      font-style: italic;
    }
  }

  // Tab切换容器
  .tab-container {
    display: flex;
    border-radius: 2px;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;

    .tab-item {
      flex: 1;
      padding: 0px 12px;
      text-align: center;
      color: #b8c5d1;
      background-color: rgba(25, 129, 246, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      position: relative;
      font-size: 12px;
      min-height: 30px;

      .tab-icon {
        font-size: 14px;
        opacity: 0.8;
      }

      .tab-text {
        font-size: 12px;
        font-weight: 400;
        white-space: nowrap;
      }

      .tab-count {
        font-size: 11px;
        color: #8fa4b3;
        margin-left: 2px;
      }

      &:hover {
        background-color: rgba(25, 129, 246, 0.2);
        color: #ffffff;

        .tab-icon {
          opacity: 1;
        }
      }

      &.active {
        background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
        color: #ffffff;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(25, 129, 246, 0.3);

        .tab-icon {
          opacity: 1;
        }

        .tab-count {
          color: #ffffff;
          opacity: 0.9;
        }
      }

      &:not(:last-child) {
        border-right: 1px solid rgba(255, 255, 255, 0.08);
      }
    }
  }

  // 统计概览
  .stats-overview {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .stat-label {
        font-size: 12px;
        color: #b8c5d1;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #03d3ec;
        text-shadow: 0 0 10px rgba(3, 211, 236, 0.5);
      }
    }
  }

  // 表格内容区域
  .board-content {
    flex: 1;
    overflow: hidden;
    margin-top: 10px;
  }

  // 图片轮播样式
  .image-carousel {
    width: 360px;
    height: 60px;
    overflow: hidden;
    border-radius: 6px;
    border: 1px solid rgba(1, 153, 209, 0.3);
    background: rgba(0, 0, 0, 0.2);

    .carousel-container {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
    }

    .carousel-track {
      display: flex;
      height: 100%;
      transition: none; // 移除transition以实现平滑滚动
    }

    .carousel-item {
      width: 60px;
      height: 100%;
      flex-shrink: 0;
      padding: 2px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
        filter: brightness(0.9);
        transition: filter 0.3s ease;

        &:hover {
          filter: brightness(1.1);
        }
      }
    }
  }

  // 自定义滚动表格样式
  /deep/ .dv-scroll-board {
    .header {
      background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
      box-shadow: 0 2px 8px rgba(25, 129, 246, 0.3);

      .header-item {
        color: #ffffff;
        font-weight: 600;
        font-size: 13px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    .rows {
      .row-item {
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(25, 129, 246, 0.2) !important;
          transform: translateX(3px);
        }

        .ceil {
          color: #ffffff;
          font-size: 12px;

          // 学生姓名列
          &:nth-child(2) {
            color: #64b5f6;
            font-weight: 500;
          }

          // 学号列
          &:nth-child(3) {
            color: #81c784;
            font-weight: 500;
          }

          // 时间列
          &:nth-child(5) {
            color: #ffb74d;
            font-weight: 500;
          }

          // 通信状态列
          &:last-child {
            color: #4caf50;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
