<template>
  <div id="data-view">
    <dv-full-screen-container>
      <!-- 上部区域：标题 + 数据概览 -->
      <div class="top-section">
        <top-header />
        <digital-flop />
      </div>
   <!-- <div class="footer-section">
        <cards />
      </div> -->
      <!-- 中部区域：主要数据展示 -->
      <div class="middle-section">
        <div class="middle-left">
          <college-table />
        </div>
        <div class="middle-center">
          <rose-chart />
        </div>
        <div class="middle-right">
          <student-communication-records />
        </div>
      </div>

      <!-- 下部区域：学院表格和其他组件 -->
      <div class="bottom-section">
        <div class="bottom-left">
          <rankingBoard />
        </div>
        <div class="bottom-center">
          <water-level-chart />
        </div>
        <div class="bottom-right">
          <scroll-board />
        </div>
      </div>

      <!-- 最底部：车辆状态条 -->

    </dv-full-screen-container>
  </div>
</template>

<script>
import topHeader from './topHeader'
import digitalFlop from './digitalFlop'
import rankingBoard from './rankingBoard'
import roseChart from './roseChart'
import waterLevelChart from './waterLevelChart'
import scrollBoard from './scrollBoard'
import cards from './cards'
import collegeTable from './collegeTable'
import studentCommunicationRecords from './studentCommunicationRecords'

export default {
  name: 'DataView',
  components: {
    topHeader,
    digitalFlop,
    rankingBoard,
    roseChart,
    waterLevelChart,
    scrollBoard,
    cards,
    collegeTable,
    studentCommunicationRecords
  },
  data () {
    return {}
  },
  methods: {}
}
</script>

<style lang="less">
#data-view {
  width: 100%;
  height: 100%;
  background-color: #030409;
  color: #fff;

  #dv-full-screen-container {
    background-image: url('./img/bg.png');
    background-size: 100% 100%;
    box-shadow: 0 0 3px blue;
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 10px;
    box-sizing: border-box;
  }

  // 上部区域：标题 + 数据概览 (25%)
  .top-section {
    display: flex;
    flex-direction: column;
  }

  // 中部区域：主要数据展示 (55%)
  .middle-section {
    height: 40%;
    display: flex;
    gap: 17px;
    margin-bottom: 15px;
    margin-top: 15px;
    padding: 0 15px;
  }

  .middle-left {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .middle-center {
    flex: 1.33;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .middle-right {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  // 下部区域：学院表格和其他组件 (20%)
  .bottom-section {
    height: 30%;
    display: flex;
    gap: 17px;
    padding: 0 15px;
  }

  .bottom-left {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
  .bottom-center {
    flex: 1.33;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
  .bottom-right {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  // 最底部区域：车辆状态条
  .footer-section {
    height: 80px;
    padding: 0 15px;
    display: flex;
    align-items: center;
  }
}
</style>
